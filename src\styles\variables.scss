// ===== 设计令牌系统 =====
// UniApp跨端项目样式变量定义

// ===== 颜色系统 =====

// 主色调
$primary-color: #1989fa !default; // 品牌蓝
$primary-light: #66b1ff !default;
$primary-dark: #0d7377 !default;

// 功能色
$success-color: #07c160 !default; // 成功绿
$warning-color: #ff976a !default; // 警告橙
$danger-color: #ee0a24 !default;  // 危险红
$info-color: #909399 !default;    // 信息灰

// 中性色
$text-color-primary: #323233 !default;   // 文本主色
$text-color-regular: #646566 !default;   // 常规色
$text-color-secondary: #969799 !default; // 次要色
$text-color-placeholder: #c8c9cc !default; // 占位符色

// 背景色
$bg-color-primary: #ffffff !default;   // 主背景白
$bg-color-secondary: #f7f8fa !default; // 次背景灰
$bg-color-tertiary: #ebedf0 !default;  // 三级背景

// 边框色
$border-color-light: #ebedf0 !default; // 浅色边框
$border-color-base: #dcdee0 !default;  // 基础边框
$border-color-dark: #c8c9cc !default;  // 深色边框

// ===== 间距系统（rpx单位，自动适配） =====
$space-xs: 8rpx !default;   // 超小间距
$space-sm: 16rpx !default;  // 小间距
$space-md: 24rpx !default;  // 中等间距
$space-lg: 32rpx !default;  // 大间距
$space-xl: 48rpx !default;  // 超大间距
$space-xxl: 64rpx !default; // 特大间距

// 页面间距
$page-padding: $space-lg !default;
$section-padding: $space-md !default;

// ===== 字体系统 =====
$font-size-xs: 20rpx !default;  // 超小字体
$font-size-sm: 24rpx !default;  // 小字体
$font-size-md: 28rpx !default;  // 中等字体（默认）
$font-size-lg: 32rpx !default;  // 大字体
$font-size-xl: 36rpx !default;  // 超大字体
$font-size-xxl: 40rpx !default; // 特大字体

// 字体粗细
$font-weight-light: 300 !default;
$font-weight-normal: 400 !default;
$font-weight-medium: 500 !default;
$font-weight-bold: 600 !default;

// 行高
$line-height-xs: 1.2 !default;
$line-height-sm: 1.4 !default;
$line-height-md: 1.5 !default;
$line-height-lg: 1.6 !default;

// ===== 圆角系统 =====
$border-radius-sm: 4rpx !default;  // 小圆角
$border-radius-md: 8rpx !default;  // 中等圆角
$border-radius-lg: 12rpx !default; // 大圆角
$border-radius-xl: 16rpx !default; // 超大圆角
$border-radius-round: 50% !default; // 圆形

// ===== 阴影系统 =====
$box-shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !default; // 轻阴影
$box-shadow-base: 0 4rpx 12rpx rgba(0, 0, 0, 0.1) !default;  // 基础阴影
$box-shadow-dark: 0 8rpx 24rpx rgba(0, 0, 0, 0.15) !default; // 深阴影

// ===== 动画系统 =====
$transition-duration-fast: 0.2s !default;
$transition-duration-base: 0.3s !default;
$transition-duration-slow: 0.5s !default;

$transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !default;

// ===== Z-index层级 =====
$z-index-dropdown: 1000 !default;
$z-index-sticky: 1020 !default;
$z-index-fixed: 1030 !default;
$z-index-modal-backdrop: 1040 !default;
$z-index-modal: 1050 !default;
$z-index-popover: 1060 !default;
$z-index-tooltip: 1070 !default;

// ===== 组件特定变量 =====

// 按钮
$button-height-large: 100rpx !default;
$button-height-normal: 88rpx !default;
$button-height-small: 64rpx !default;
$button-height-mini: 48rpx !default;

$button-font-size-large: $font-size-lg !default;
$button-font-size-normal: $font-size-md !default;
$button-font-size-small: $font-size-sm !default;
$button-font-size-mini: $font-size-xs !default;

// 输入框
$input-height: 88rpx !default;
$input-padding: $space-md !default;
$input-border-radius: $border-radius-md !default;

// 导航栏
$navbar-height: 88rpx !default;
$navbar-background: $bg-color-primary !default;

// TabBar
$tabbar-height: 100rpx !default;
$tabbar-background: $bg-color-primary !default;

// ===== 平台特定变量 =====

// 安全区域
$safe-area-inset-top: env(safe-area-inset-top) !default;
$safe-area-inset-bottom: env(safe-area-inset-bottom) !default;
$safe-area-inset-left: env(safe-area-inset-left) !default;
$safe-area-inset-right: env(safe-area-inset-right) !default;
