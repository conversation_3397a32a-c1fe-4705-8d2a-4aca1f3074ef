/**
 * 会议相关 API
 */

import { get, post, put, del } from '../request'

/**
 * 会议 API 接口
 */
export const meetingApi = {
  /**
   * 获取会议参与者列表
   * @param params 查询参数
   */
  getParticipantList: (params?: Record<string, any>) => {
    return get('/meeting/participant/list', params)
  },

  /**
   * 获取会议列表
   * @param params 查询参数
   */
  getMeetingList: (params?: Record<string, any>) => {
    return get('/meeting/list', params)
  },

  /**
   * 获取会议详情
   * @param id 会议ID
   */
  getMeetingDetail: (id: string | number) => {
    return get(`/meeting/detail/${id}`)
  },

  /**
   * 创建会议
   * @param data 会议数据
   */
  createMeeting: (data: Record<string, any>) => {
    return post('/meeting/create', data)
  },

  /**
   * 更新会议
   * @param id 会议ID
   * @param data 更新数据
   */
  updateMeeting: (id: string | number, data: Record<string, any>) => {
    return put(`/meeting/update/${id}`, data)
  },

  /**
   * 删除会议
   * @param id 会议ID
   */
  deleteMeeting: (id: string | number) => {
    return del(`/meeting/delete/${id}`)
  }
}
