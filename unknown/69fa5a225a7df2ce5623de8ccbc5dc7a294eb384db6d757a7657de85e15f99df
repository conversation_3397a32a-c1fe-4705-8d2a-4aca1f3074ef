# ===== 依赖目录 =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===== 构建输出 =====
dist/
unpackage/
build/

# ===== 缓存目录 =====
.cache/
.temp/
.tmp/

# ===== 日志文件 =====
*.log
logs/

# ===== 环境变量文件 =====
.env.local
.env.*.local

# ===== 编辑器配置 =====
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# ===== 系统文件 =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ===== 压缩文件 =====
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# ===== 临时文件 =====
*.tmp
*.temp
*.bak
*.backup
*.orig

# ===== 测试覆盖率 =====
coverage/
*.lcov
.nyc_output/

# ===== 包管理器锁文件（可选择性忽略） =====
# 如果团队使用统一的包管理器，可以取消注释对应行
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# ===== UniApp特定文件 =====
# 微信小程序工具生成的文件
.wxs
project.config.json
project.private.config.json

# 支付宝小程序工具生成的文件
.tea/

# 百度小程序工具生成的文件
.swan/

# 字节跳动小程序工具生成的文件
.taro/

# QQ小程序工具生成的文件
.qqmp/

# ===== 开发工具生成的文件 =====
# HBuilderX
.hbuilderx/

# 微信开发者工具
.wechat_devtools/

# ===== 证书和密钥文件 =====
*.pem
*.key
*.crt
*.p12
*.mobileprovision

# ===== 其他 =====
# 错误报告
error-report.txt

# 性能分析文件
*.cpuprofile
*.heapprofile

# 调试文件
debug.log

# 本地配置文件
local.config.js
local.config.ts
