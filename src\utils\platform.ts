/**
 * 平台判断工具
 * 提供跨平台的运行时检测、系统信息获取、UI交互和页面导航功能
 */

// ===== 平台判断方法 =====

/**
 * 检测是否为H5平台
 */
export function isH5(): boolean {
  // #ifdef H5
  return true
  // #endif
  // #ifndef H5
  return false
  // #endif
}

/**
 * 检测是否为小程序平台
 */
export function isMp(): boolean {
  // #ifdef MP
  return true
  // #endif
  // #ifndef MP
  return false
  // #endif
}

/**
 * 检测是否为微信小程序
 */
export function isMpWeixin(): boolean {
  // #ifdef MP-WEIXIN
  return true
  // #endif
  // #ifndef MP-WEIXIN
  return false
  // #endif
}

/**
 * 检测是否为App平台
 */
export function isApp(): boolean {
  // #ifdef APP
  return true
  // #endif
  // #ifndef APP
  return false
  // #endif
}

/**
 * 获取当前平台名称
 */
export function getPlatform(): string {
  // #ifdef H5
  return 'h5'
  // #endif
  // #ifdef MP-WEIXIN
  return 'mp-weixin'
  // #endif
  // #ifdef APP
  return 'app'
  // #endif
  return 'unknown'
}

// ===== 系统信息获取 =====

/**
 * 获取设备系统信息
 */
export function getSystemInfo(): Promise<UniApp.GetSystemInfoResult> {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

/**
 * 获取设备信息（同步）
 */
export function getSystemInfoSync(): UniApp.GetSystemInfoResult {
  return uni.getSystemInfoSync()
}

// ===== UI交互适配 =====

/**
 * 显示提示消息
 */
export function showToast(options: {
  title: string
  icon?: 'success' | 'error' | 'loading' | 'none'
  duration?: number
  mask?: boolean
}): void {
  // #ifdef H5
  // H5平台使用Vant的showToast（需要在组件中导入使用）
  // 这里使用uni.showToast作为fallback
  uni.showToast({
    title: options.title,
    icon: options.icon || 'none',
    duration: options.duration || 2000,
    mask: options.mask || false
  })
  // #endif

  // #ifdef MP
  // 小程序使用uni.showToast
  uni.showToast({
    title: options.title,
    icon: options.icon || 'none',
    duration: options.duration || 2000,
    mask: options.mask || false
  })
  // #endif
}

/**
 * 显示加载状态
 */
export function showLoading(options: {
  title?: string
  mask?: boolean
}): void {
  // #ifdef H5
  // H5平台使用Vant的showLoadingToast（需要在组件中导入使用）
  // 这里使用uni.showLoading作为fallback
  uni.showLoading({
    title: options.title || '加载中...',
    mask: options.mask !== false
  })
  // #endif

  // #ifdef MP
  // 小程序使用uni.showLoading
  uni.showLoading({
    title: options.title || '加载中...',
    mask: options.mask !== false
  })
  // #endif
}

/**
 * 隐藏加载状态
 */
export function hideLoading(): void {
  uni.hideLoading()
}

/**
 * 显示模态对话框
 */
export function showModal(options: {
  title?: string
  content: string
  showCancel?: boolean
  cancelText?: string
  confirmText?: string
}): Promise<{ confirm: boolean; cancel: boolean }> {
  return new Promise((resolve) => {
    uni.showModal({
      title: options.title || '提示',
      content: options.content,
      showCancel: options.showCancel !== false,
      cancelText: options.cancelText || '取消',
      confirmText: options.confirmText || '确定',
      success: (res) => {
        resolve({
          confirm: res.confirm,
          cancel: res.cancel
        })
      }
    })
  })
}

// ===== 页面导航 =====

/**
 * 保留当前页面，跳转到新页面
 */
export function navigateTo(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.navigateTo({
      url,
      success: () => resolve(),
      fail: (err) => reject(err)
    })
  })
}

/**
 * 关闭当前页面，跳转到新页面
 */
export function redirectTo(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.redirectTo({
      url,
      success: () => resolve(),
      fail: (err) => reject(err)
    })
  })
}

/**
 * 返回上一页面
 */
export function navigateBack(delta: number = 1): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.navigateBack({
      delta,
      success: () => resolve(),
      fail: (err) => reject(err)
    })
  })
}

/**
 * 跳转到tabBar页面
 */
export function switchTab(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.switchTab({
      url,
      success: () => resolve(),
      fail: (err) => reject(err)
    })
  })
}

/**
 * 关闭所有页面，打开到应用内的某个页面
 */
export function reLaunch(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    uni.reLaunch({
      url,
      success: () => resolve(),
      fail: (err) => reject(err)
    })
  })
}
