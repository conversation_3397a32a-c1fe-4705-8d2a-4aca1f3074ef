{"name": "my-uniapp-project", "version": "1.0.0", "description": "UniApp跨端项目 - 支持H5、微信小程序和App", "main": "main.js", "scripts": {"dev:h5": "uni build --platform h5 --mode development --watch", "dev:mp-weixin": "uni build --platform mp-weixin --mode development --watch", "dev:app": "uni build --platform app --mode development --watch", "build:h5": "uni build --platform h5 --mode production", "build:mp-weixin": "uni build --platform mp-weixin --mode production", "build:app": "uni build --platform app --mode production", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx", "lint:fix": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "preview": "vite preview"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-4020920240930001", "@dcloudio/uni-components": "^3.0.0-4020920240930001", "@dcloudio/uni-h5": "^3.0.0-4020920240930001", "@dcloudio/uni-mp-weixin": "^3.0.0-4020920240930001", "@vitejs/plugin-vue": "^6.0.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.3", "vue": "^3.4.0"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "^3.0.0-4020920240930001", "@dcloudio/uni-cli-shared": "^3.0.0-4020920240930001", "@dcloudio/vite-plugin-uni": "^3.0.0-4020920240930001", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.20.1", "prettier": "^3.2.5", "sass": "^1.70.0", "typescript": "^5.3.3", "vite": "^5.1.0", "vue-tsc": "^1.8.27"}, "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "uni-app": {"scripts": {}}}