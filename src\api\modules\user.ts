/**
 * 用户API模块
 * 提供用户相关的接口方法
 */

import { get, post, put } from '../request'
import type {
  UserInfo,
  LoginParams,
  LoginResult,
  RegisterParams,
  ChangePasswordParams
} from '../types'

// ===== 用户认证相关 =====

/**
 * 用户登录
 */
export function login(params: LoginParams): Promise<LoginResult> {
  return post<LoginResult>('/auth/login', params)
}

/**
 * 用户注册
 */
export function register(params: RegisterParams): Promise<UserInfo> {
  return post<UserInfo>('/auth/register', params)
}

/**
 * 退出登录
 */
export function logout(): Promise<void> {
  return post<void>('/auth/logout')
}

/**
 * 刷新token
 */
export function refreshToken(refreshToken: string): Promise<LoginResult> {
  return post<LoginResult>('/auth/refresh', { refreshToken })
}

/**
 * 发送验证码
 */
export function sendCaptcha(phone: string): Promise<void> {
  return post<void>('/auth/captcha', { phone })
}

// ===== 用户信息相关 =====

/**
 * 获取用户信息
 */
export function getUserInfo(): Promise<UserInfo> {
  return get<UserInfo>('/user/info')
}

/**
 * 更新用户信息
 */
export function updateUserInfo(userInfo: Partial<UserInfo>): Promise<UserInfo> {
  return put<UserInfo>('/user/info', userInfo)
}

/**
 * 修改密码
 */
export function changePassword(params: ChangePasswordParams): Promise<void> {
  return put<void>('/user/password', params)
}

/**
 * 上传头像
 */
export function uploadAvatar(filePath: string): Promise<{ url: string }> {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `${import.meta.env.VITE_API_BASE_URL}/user/avatar`,
      filePath,
      name: 'avatar',
      header: {
        'Authorization': `Bearer ${uni.getStorageSync('token')}`
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          if (data.code === 200) {
            resolve(data.data)
          } else {
            reject(new Error(data.message))
          }
        } catch (error) {
          reject(error)
        }
      },
      fail: reject
    })
  })
}

// ===== 用户设置相关 =====

/**
 * 获取用户设置
 */
export function getUserSettings(): Promise<Record<string, any>> {
  return get<Record<string, any>>('/user/settings')
}

/**
 * 更新用户设置
 */
export function updateUserSettings(settings: Record<string, any>): Promise<void> {
  return put<void>('/user/settings', settings)
}

// ===== 默认导出 =====
export default {
  // 认证相关
  login,
  register,
  logout,
  refreshToken,
  sendCaptcha,
  
  // 用户信息相关
  getUserInfo,
  updateUserInfo,
  changePassword,
  uploadAvatar,
  
  // 用户设置相关
  getUserSettings,
  updateUserSettings
}
