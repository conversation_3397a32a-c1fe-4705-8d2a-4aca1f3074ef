/**
 * API模块入口文件
 * 统一导出所有API模块
 */

// 导出请求方法
export { default as request, get, post, put, del as delete } from './request'

// 导出类型定义
export * from './types'

// 导出API模块
export { default as userApi } from './modules/user'
export { meetingApi } from './modules/meeting'

// 统一API对象
import userApi from './modules/user'
import { meetingApi } from './modules/meeting'

export const api = {
  user: userApi,
  meeting: meetingApi
}

export default api
