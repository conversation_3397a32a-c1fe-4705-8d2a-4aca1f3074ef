import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni()
  ],

  // 路径别名配置
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, 'src')
      },
      {
        find: '@utils',
        replacement: resolve(__dirname, 'src/utils')
      },
      {
        find: '@api',
        replacement: resolve(__dirname, 'src/api')
      },
      {
        find: '@store',
        replacement: resolve(__dirname, 'src/store')
      },
      {
        find: '@types',
        replacement: resolve(__dirname, 'src/types')
      },
      {
        find: '@styles',
        replacement: resolve(__dirname, 'src/styles')
      },
      {
        find: '@static',
        replacement: resolve(__dirname, 'src/static')
      },

    ]
  },

  // CSS预处理器配置
  css: {
    preprocessorOptions: {
      scss: {
        // SCSS 预处理器配置
        charset: false
      }
    }
  },

  // 环境变量配置
  define: {
    __PLATFORM__: JSON.stringify(process.env.UNI_PLATFORM),
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },

  // 开发服务器配置
  server: {
    port: 3000,
    host: '0.0.0.0', // 支持局域网访问
    open: true,
    cors: true,
    // 代理配置
    proxy: {
      // 会议支持服务代理
      '/hb/mice-support/api': {
        target: 'https://travel-test.haier.net/hbweb/mice-support/hb/mice-support/api/',
        changeOrigin: true,
        rewrite: (path) => path.replace(new RegExp(`/hb/mice-support/api`), ''),
      },
      // 高德地图API代理
      '/hb/gd': {
        target: 'https://restapi.amap.com/v5',
        changeOrigin: true,
        rewrite: (path) => path.replace(new RegExp(`/hb/gd`), ''),
      },
      // 通用上传接口
      '/hb/common/api': {
        target: 'https://travel-test.haier.net/hbweb/index/hb',
        changeOrigin: true,
        rewrite: (path) => path.replace(new RegExp(`/hb`), ''),
      },
      // 通用接口代理
      '/hb': {
        target: 'https://travel-test.haier.net/hbweb/index/hb',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/hb/, ''),
      },
      // 文件上传代理
      '/upload': {
        target: 'https://travel-test.haier.net/hbweb/upload',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/upload/, ''),
      },
    }
  },

  // 构建配置
  build: {
    target: 'es2015',
    cssTarget: 'chrome61',
    rollupOptions: {
      external: [],
      output: {
        globals: {}
      }
    }
  },

  // 优化配置
  optimizeDeps: {
    include: []
  }
})
