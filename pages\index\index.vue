<template>
  <view class="content">
    <view class="text-area">
      <text class="title">{{ title }}</text>
    </view>

    <!-- 业务功能区域 -->
    <view class="uni-demo">
      <view class="button-group">
        <button type="default" @click="getParticipantList" class="uni-button" :disabled="loading">
          {{ loading ? '加载中...' : '刷新数据' }}
        </button>
      </view>

      <!-- 参与者列表 -->
      <view class="card" v-if="participantList.length > 0">
        <view class="card-title">会议参与者列表 ({{ participantList.length }})</view>
        <view class="cell-group">
          <view class="cell" v-for="(item, index) in participantList" :key="item.id">
            <view class="participant-info">
              <text class="participant-name">{{ item.nickName || item.userName }}</text>
              <text class="participant-company">{{ item.enterpriseName }}</text>
            </view>
            <text class="participant-id">ID: {{ item.id }}</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="card" v-else-if="!loading">
        <view class="card-title">会议参与者列表</view>
        <view class="empty-state">
          <text>暂无数据</text>
        </view>
      </view>


    </view>
  </view>
</template>

<script>
import { meetingApi } from '@/src/api/modules/meeting'

export default {
  data() {
    return {
      title: '会议管理系统',
      participantList: [], // 参与者列表
      loading: false // 加载状态
    }
  },
  onLoad() {
    // 页面加载时调用接口
    this.getParticipantList()
  },

  methods: {
    /**
     * 检查 Token 是否过期
     * @param {string} token JWT Token
     * @returns {boolean} true-过期，false-未过期
     */
    isTokenExpired(token) {
      try {
        const tokenParts = token.split('.')
        if (tokenParts.length !== 3) return true

        // 修复 base64 padding
        let payload = tokenParts[1]
        while (payload.length % 4) {
          payload += '='
        }

        const decodedPayload = JSON.parse(atob(payload))
        const now = Math.floor(Date.now() / 1000)
        const isExpired = now > decodedPayload.exp

        if (isExpired) {
          console.warn('⚠️ Token 已过期！')
          console.log('过期时间:', new Date(decodedPayload.exp * 1000))
          console.log('当前时间:', new Date())
        }

        return isExpired
      } catch (e) {
        console.error('Token 解码失败:', e)
        return true
      }
    },

    /**
     * 获取有效的 Token
     * @returns {string} 有效的 token 或空字符串
     */
    getValidToken() {
      // 从本地存储获取 token
      let token = uni.getStorageSync('hb-token') || this.currentToken

      // 如果没有 token，使用硬编码的 token（临时方案）
      if (!token) {
        token = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
      }

      // 检查 token 是否过期
      if (this.isTokenExpired(token)) {
        uni.showModal({
          title: 'Token 已过期',
          content: '请重新获取 Token 或联系管理员',
          showCancel: false
        })
        return ''
      }

      return token
    },

    /**
     * 保存新的 Token
     * @param {string} newToken 新的 token
     */
    saveToken(newToken) {
      if (newToken) {
        this.currentToken = newToken
        uni.setStorageSync('hb-token', newToken)
        console.log('✅ 新 Token 已保存')
      }
    },
    /**
     * 获取会议参与者列表
     * @param {Object} params 请求参数
     */
    async getParticipantList(params = { miceInfoId: 1 }) {
      try {
        this.loading = true
        console.log('开始调用接口: /meeting/participant/list')

        // 根据平台使用不同的 URL
        let apiUrl = ''
        // #ifdef H5
        // H5 环境使用代理路径
        apiUrl = '/hb/mice-support/api/meeting/participant/list'
        // #endif

        // #ifdef MP || APP
        // 小程序和 App 使用完整 URL
        apiUrl =
          'https://travel-test.haier.net/hbweb/mice-support/hb/mice-support/api/meeting/participant/list'
        // #endif

        console.log('当前平台 API URL:', apiUrl)

        // 准备请求头
        const token =
          '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'

        const headers = {
          'Content-Type': 'application/json',
          'hb-token': token // 使用 hb-token 而不是 Authorization
        }

        console.log('请求头信息:', headers)
        console.log('🔑 Token 长度:', token.length)

        // 解码 JWT Token 检查过期时间
        try {
          const tokenParts = token.split('.')
          if (tokenParts.length === 3) {
            // JWT 使用 base64url 编码，需要转换为标准 base64
            function base64urlToBase64(str) {
              // 替换 base64url 特殊字符
              str = str.replace(/-/g, '+').replace(/_/g, '/')
              // 添加 padding
              while (str.length % 4) {
                str += '='
              }
              return str
            }
            
            let payload = tokenParts[1]
            console.log('🔧 原始 payload:', payload)
            
            // 转换为标准 base64
            const base64Payload = base64urlToBase64(payload)
            console.log('🔧 转换后 base64:', base64Payload)
            
            const decodedPayload = JSON.parse(atob(base64Payload))
            console.log('🔓 解码成功:', decodedPayload)
            
            const now = Math.floor(Date.now() / 1000)
            const expTime = new Date(decodedPayload.exp * 1000)
            const currentTime = new Date()
            const isExpired = now > decodedPayload.exp
            
            console.log('🕐 Token 过期时间:', expTime.toLocaleString())
            console.log('🕐 当前时间:', currentTime.toLocaleString())
            console.log('⏰ Token 是否过期:', isExpired)
            
            // 安全地解析用户信息
            let userInfo = { username: 'unknown', nickName: 'unknown' }
            try {
              if (decodedPayload.sub && typeof decodedPayload.sub === 'string') {
                const subData = JSON.parse(decodedPayload.sub)
                userInfo = {
                  username: subData.username || 'unknown',
                  nickName: subData.nickName || 'unknown',
                  id: subData.id || 'unknown'
                }
              }
            } catch (subError) {
              console.log('⚠️ 用户信息解析失败:', subError)
            }
            
            console.log('👤 用户信息:', userInfo)

            if (isExpired) {
              console.error('❌ Token 已过期！')
              uni.showToast({
                title: 'Token 已过期，请重新登录',
                icon: 'error',
                duration: 3000
              })
              return
            } else {
              const remainingTime = Math.floor((decodedPayload.exp - now) / 60)
              console.log('✅ Token 有效，剩余时间:', remainingTime, '分钟')
            }
          } else {
            console.error('❌ Token 格式错误，部分数量:', tokenParts.length)
            throw new Error('Token 格式不正确')
          }
        } catch (e) {
          console.error('❌ Token 解码失败详情:', e)
          console.error('Token 前50个字符:', token.substring(0, 50) + '...')
          
          // 不再因为 Token 解码失败而中断请求，只是警告
          console.warn('⚠️ Token 解码失败，但继续执行请求')
          uni.showToast({
            title: 'Token 解析警告，继续尝试请求',
            icon: 'none',
            duration: 2000
          })
          // 移除 return，让请求继续执行
        }

        // 先尝试不带参数的请求
        const fullUrl = apiUrl
        // 如果需要参数，可以这样添加：
        // const fullUrl = `${apiUrl}?page=1&size=10`

        console.log('完整请求 URL:', fullUrl)

        // 准备请求参数 - 合并默认参数和传入参数
        const requestParams = {
          page: 1,
          size: 10,
          ...params // 展开传入的参数
        }

        console.log('请求参数:', requestParams)

        // 直接使用 uni.request 调用接口
        const result = await new Promise((resolve, reject) => {
          uni.request({
            url: apiUrl, // 使用原始 URL
            method: 'GET',
            data: requestParams, // GET 请求的参数
            header: headers,
            success: res => {
              console.log('接口响应:', res)
              console.log('响应状态码:', res.statusCode)
              console.log('响应数据:', res.data)
              console.log('响应头:', res.header)

              // 检查是否有新的 token
              if (res.header && res.header['hb-token']) {
                console.log('🔄 收到新的 token:', res.header['hb-token'])
                // 这里可以保存新的 token 到本地存储
                // uni.setStorageSync('hb-token', res.header['hb-token'])
              }

              if (res.statusCode === 200) {
                resolve(res.data)
              } else {
                const errorMsg = `HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`
                reject(new Error(errorMsg))
              }
            },
            fail: err => {
              console.error('请求失败:', err)
              reject(err)
            }
          })
        })

        console.log('🎉 接口调用成功:', result)
        console.log('📊 完整响应结构:', JSON.stringify(result, null, 2))
        
        // 检查数据结构
        console.log('🔍 数据解析路径检查:')
        console.log('result?.data:', result?.data)
        console.log('result?.data?.list:', result?.data?.list)
        console.log('result?.data?.list?.records:', result?.data?.list?.records)
        
        // 尝试多种可能的数据路径
        let participantData = null
        if (result?.data?.list?.records) {
          participantData = result.data.list.records
          console.log('✅ 使用路径: result.data.list.records')
        } else if (result?.data?.records) {
          participantData = result.data.records
          console.log('✅ 使用路径: result.data.records')
        } else if (result?.data && Array.isArray(result.data)) {
          participantData = result.data
          console.log('✅ 使用路径: result.data (直接数组)')
        } else if (result?.list?.records) {
          participantData = result.list.records
          console.log('✅ 使用路径: result.list.records')
        } else {
          console.log('❌ 未找到有效的数据路径')
          console.log('🔧 可用的数据字段:', Object.keys(result || {}))
        }
        
        // 设置参与者列表
        this.participantList = participantData || []
        console.log('👥 最终参与者列表:', this.participantList)
        console.log('📈 参与者数量:', this.participantList.length)
        
        if (this.participantList.length > 0) {
          console.log('👤 第一个参与者示例:', this.participantList[0])
        }

        console.log('📊 其他统计信息:')
        console.log('总数:', result?.data?.list?.total || result?.data?.total || 0)
        console.log('审批统计:', {
          通过: result?.data?.approvePassCount || 0,
          等待: result?.data?.approveWaitCount || 0,
          拒绝: result?.data?.approveRejectCount || 0
        })

        // 显示更详细的成功信息
        const successMsg = this.participantList.length > 0 
          ? `加载成功，获取到 ${this.participantList.length} 条数据`
          : '加载完成，但没有数据'
        
        uni.showToast({
          title: successMsg,
          icon: this.participantList.length > 0 ? 'success' : 'none',
          duration: 2000
        })
      } catch (error) {
        console.error('❌ 接口调用失败详情:', error)
        console.error('错误类型:', error.constructor.name)
        console.error('错误消息:', error.message)
        console.error('错误堆栈:', error.stack)
        
        let errorMessage = '数据加载失败'
        if (error.message.includes('Token 已过期')) {
          errorMessage = 'Token 已过期，请重新登录'
        } else if (error.message.includes('HTTP 401')) {
          errorMessage = '认证失败，请检查登录状态'
        } else if (error.message.includes('HTTP 403')) {
          errorMessage = '权限不足'
        } else if (error.message.includes('HTTP 404')) {
          errorMessage = '接口不存在'
        } else if (error.message.includes('HTTP 500')) {
          errorMessage = '服务器内部错误'
        } else if (error.message.includes('timeout')) {
          errorMessage = '请求超时，请检查网络'
        } else if (error.message.includes('network')) {
          errorMessage = '网络连接失败'
        } else {
          errorMessage = `加载失败: ${error.message}`
        }
        
        uni.showToast({
          title: errorMessage,
          icon: 'error',
          duration: 3000
        })
        
        // 清空之前的数据
        this.participantList = []
      } finally {
        this.loading = false
        console.log('🏁 请求完成，loading 状态已重置')
      }
    },


  }
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 40rpx;
  min-height: 100vh;
}

.logo {
  height: 150rpx;
  width: 150rpx;
  margin-bottom: 30rpx;
}

.text-area {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}

.uni-demo {
  width: 100%;
  max-width: 600rpx;
}

.card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #323233;
  border-bottom: 1rpx solid #ebedf0;
}

.cell-group {
  background: #fff;
}

.cell {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #ebedf0;
  position: relative;
}

.cell:last-child {
  border-bottom: none;
}

.cell-title {
  flex: 1;
  font-size: 28rpx;
  color: #323233;
}

.cell-value {
  font-size: 28rpx;
  color: #969799;
  margin-right: 20rpx;
}

.cell-icon {
  font-size: 32rpx;
}

.button-group {
  margin: 30rpx 0;
  display: flex;
  gap: 20rpx;
}

.uni-button {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.uni-button.success {
  background-color: #07c160;
  color: white;
}



/* 空状态样式 */
.empty-state {
  padding: 60rpx 30rpx;
  text-align: center;
  color: #969799;
  font-size: 28rpx;
}

/* 加载状态按钮 */
.uni-button:disabled {
  opacity: 0.6;
}

/* 参与者信息样式 */
.participant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.participant-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #323233;
  margin-bottom: 8rpx;
}

.participant-company {
  font-size: 24rpx;
  color: #969799;
}

.participant-id {
  font-size: 24rpx;
  color: #1989fa;
  align-self: flex-end;
}
</style>
