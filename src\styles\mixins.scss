// ===== UniApp 项目样式混入 =====

// ===== 布局混入 =====

// 水平居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 水平左对齐
@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

// 垂直布局
@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// ===== 文本混入 =====

// 单行文本省略
@mixin text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 多行文本省略
@mixin text-ellipsis-multi($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// ===== 两端对齐 =====
@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
