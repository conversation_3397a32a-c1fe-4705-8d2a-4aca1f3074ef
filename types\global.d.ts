/**
 * 全局类型声明
 */

/// <reference types="@dcloudio/types" />

declare global {
  // UniApp全局变量
  const uni: UniApp.Uni
  const wx: any
  const getCurrentPages: () => any[]
  const getApp: () => any

  // Vue全局API
  const defineProps: typeof import('vue')['defineProps']
  const defineEmits: typeof import('vue')['defineEmits']
  const defineExpose: typeof import('vue')['defineExpose']
  const withDefaults: typeof import('vue')['withDefaults']

  // 平台标识
  const __PLATFORM__: string

  // 全局接口
  interface Window {
    // 微信小程序
    wx?: any
    // 支付宝小程序
    my?: any
    // 百度小程序
    swan?: any
    // 字节跳动小程序
    tt?: any
    // QQ小程序
    qq?: any
  }
}

export {}
